@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* ZARA-inspired neutral palette */
    --background: 0 0% 98%;
    --foreground: 210 15% 15%;

    --card: 0 0% 100%;
    --card-foreground: 210 15% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 15% 15%;

    --primary: 210 15% 15%;
    --primary-foreground: 0 0% 98%;

    --secondary: 220 10% 92%;
    --secondary-foreground: 210 15% 15%;

    --muted: 220 10% 95%;
    --muted-foreground: 210 12% 45%;

    --accent: 25 20% 85%;
    --accent-foreground: 210 15% 15%;

    --destructive: 0 65% 55%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 210 15% 15%;

    /* iOS 26 Liquid Glass effects */
    --glass-bg: 0 0% 100% / 0.08;
    --glass-border: 0 0% 100% / 0.15;
    --glass-shadow: 0 0% 0% / 0.05;
    
    /* Clear Glass Gradients */
    --gradient-glass: linear-gradient(135deg, 
      hsl(0 0% 100% / 0.1), 
      hsl(0 0% 100% / 0.02)
    );
    --gradient-shimmer: linear-gradient(110deg, 
      transparent 25%, 
      hsl(0 0% 100% / 0.2) 50%, 
      transparent 75%
    );

    --radius: 1rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

@layer components {
  /* iOS 26 Liquid Glass Panel */
  .glass-panel {
    @apply border border-white/15;
    background: linear-gradient(135deg, 
      hsl(0 0% 100% / 0.1), 
      hsl(0 0% 100% / 0.02)
    );
    box-shadow: 
      0 8px 32px hsla(var(--glass-shadow)),
      inset 0 1px 0 hsl(var(--glass-border)),
      0 0 0 1px hsl(0 0% 100% / 0.05);
  }

  /* Liquid Glass Button */
  .glass-button {
    @apply glass-panel px-8 py-4 rounded-full font-medium text-white transition-all duration-300;
    @apply hover:scale-105 hover:shadow-2xl;
    background: linear-gradient(135deg, 
      hsl(0 0% 100% / 0.12), 
      hsl(0 0% 100% / 0.04)
    );
  }

  .glass-button:hover {
    background: linear-gradient(135deg, 
      hsl(0 0% 100% / 0.18), 
      hsl(0 0% 100% / 0.08)
    );
    box-shadow: 
      0 12px 40px hsla(var(--glass-shadow)),
      inset 0 1px 0 hsl(var(--glass-border)),
      0 0 0 1px hsl(0 0% 100% / 0.08);
  }

  /* Shimmer Animation */
  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-shimmer);
    animation: shimmer 2s infinite;
  }

  /* Liquid Glass Feature Card */
  .feature-card {
    @apply glass-panel p-6 rounded-2xl transition-all duration-500;
    @apply hover:scale-105 hover:-translate-y-2;
  }

  .feature-card:hover {
    background: linear-gradient(135deg, 
      hsl(0 0% 100% / 0.15), 
      hsl(0 0% 100% / 0.05)
    );
    box-shadow: 
      0 20px 50px hsla(var(--glass-shadow)),
      inset 0 1px 0 hsl(var(--glass-border)),
      0 0 0 1px hsl(0 0% 100% / 0.08);
  }

  /* Parallax with Refraction */
  .parallax {
    transform-style: preserve-3d;
    transition: transform 0.1s ease-out;
  }

  /* Refraction Effect */
  .glass-refraction {
    position: relative;
    overflow: hidden;
  }

  .glass-refraction::before {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: 
      radial-gradient(circle at 30% 30%, hsl(0 0% 100% / 0.1) 0%, transparent 50%),
      radial-gradient(circle at 70% 70%, hsl(0 0% 100% / 0.05) 0%, transparent 50%);
    transform: rotate(0deg) scale(1);
    transition: transform 0.3s ease-out;
    pointer-events: none;
    z-index: 1;
  }

  .glass-refraction::after {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, 
      transparent 0%, 
      hsl(0 0% 100% / 0.03) 25%, 
      transparent 50%, 
      hsl(0 0% 100% / 0.03) 75%, 
      transparent 100%
    );
    transform: translateX(-100%);
    transition: transform 0.6s ease-out;
    pointer-events: none;
    z-index: 2;
  }

  /* Scroll-triggered refraction states */
  .glass-refraction.scroll-active::before {
    transform: rotate(2deg) scale(1.1);
  }

  .glass-refraction.scroll-active::after {
    transform: translateX(100%);
  }
}

@layer utilities {
  /* Animation keyframes */
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes refractionPulse {
    0%, 100% { 
      transform: scale(1) rotate(0deg);
      opacity: 0.1;
    }
    50% { 
      transform: scale(1.05) rotate(1deg);
      opacity: 0.2;
    }
  }

  @keyframes lightSweep {
    0% { transform: translateX(-100%) skewX(-15deg); }
    100% { transform: translateX(200%) skewX(-15deg); }
  }

  /* Utility classes */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-slide-in-up {
    animation: slideInUp 0.8s ease-out;
  }

  .animate-fade-in-scale {
    animation: fadeInScale 0.6s ease-out;
  }

  .animate-refraction-pulse {
    animation: refractionPulse 4s ease-in-out infinite;
  }

  .animate-light-sweep {
    animation: lightSweep 3s ease-out infinite;
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .backdrop-blur-strong {
    backdrop-filter: blur(2px);
  }
}