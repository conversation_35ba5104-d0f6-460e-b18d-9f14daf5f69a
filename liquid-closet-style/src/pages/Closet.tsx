import { useState } from "react";
import { Plus, Filter, Search } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import useScrollRefraction from "@/hooks/useScrollRefraction";

const Closet = () => {
  useScrollRefraction();
  const [activeFilter, setActiveFilter] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");

  const filters = ["All", "Tops", "Bottoms", "Outerwear", "Accessories", "Shoes"];

  const clothingItems = [
    { id: 1, name: "White Linen Shirt", category: "Tops", color: "White", season: "Summer" },
    { id: 2, name: "Dark Wash Jeans", category: "Bottoms", color: "Indigo", season: "All" },
    { id: 3, name: "Wool Blazer", category: "Outerwear", color: "Navy", season: "Winter" },
    { id: 4, name: "Leather Boots", category: "Shoes", color: "Brown", season: "Fall" },
    { id: 5, name: "Silk Scarf", category: "Accessories", color: "Beige", season: "All" },
    { id: 6, name: "Cotton T-Shirt", category: "Tops", color: "Gray", season: "All" },
    { id: 7, name: "Tailored Trousers", category: "Bottoms", color: "Black", season: "All" },
    { id: 8, name: "Cashmere Sweater", category: "Tops", color: "Cream", season: "Winter" },
  ];

  const filteredItems = clothingItems.filter(item => {
    const matchesFilter = activeFilter === "All" || item.category === activeFilter;
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  return (
    <div className="min-h-screen pb-24 pt-6">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-light text-white">My Closet</h1>
          <Dialog>
            <DialogTrigger asChild>
              <Button className="glass-panel border-white/20 text-white hover:bg-white/10 rounded-full w-12 h-12 p-0">
                <Plus className="h-5 w-5" />
              </Button>
            </DialogTrigger>
            <DialogContent className="glass-panel border-white/20 bg-black/20 backdrop-blur-xl">
              <DialogHeader>
                <DialogTitle className="text-white">Add New Item</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 pt-4">
                <Button className="w-full glass-panel border-white/20 text-white hover:bg-white/10">
                  Take Photo
                </Button>
                <Button className="w-full glass-panel border-white/20 text-white hover:bg-white/10">
                  Choose from Gallery
                </Button>
                <Button className="w-full glass-panel border-white/20 text-white hover:bg-white/10">
                  Paste URL
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Search Bar */}
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/50" />
          <Input
            placeholder="Search your wardrobe..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="glass-panel border-white/20 bg-white/5 text-white placeholder:text-white/50 pl-10"
          />
        </div>

        {/* Filter Bar */}
        <div className="glass-panel glass-refraction p-4 rounded-2xl border-white/20 mb-6">
          <div className="flex items-center space-x-2 overflow-x-auto">
            <Filter className="h-4 w-4 text-white/70 flex-shrink-0" />
            {filters.map((filter) => (
              <Button
                key={filter}
                variant={activeFilter === filter ? "default" : "ghost"}
                size="sm"
                onClick={() => setActiveFilter(filter)}
                className={`flex-shrink-0 ${
                  activeFilter === filter
                    ? "bg-white/20 text-white border-white/30"
                    : "text-white/70 hover:bg-white/10 hover:text-white"
                }`}
              >
                {filter}
              </Button>
            ))}
          </div>
        </div>

        {/* Clothing Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {filteredItems.map((item, index) => (
            <Card 
              key={item.id}
              className="glass-panel glass-refraction p-4 border-white/20 transition-all duration-300 hover:transform hover:scale-105 cursor-pointer group"
              style={{ animationDelay: `${index * 0.05}s` }}
            >
              {/* Item Image Placeholder */}
              <div className="aspect-square rounded-xl bg-white/10 mb-3 flex items-center justify-center group-hover:bg-white/15 transition-colors">
                <div className="text-white/50 text-xs text-center">
                  {item.name.split(' ').map(word => word[0]).join('')}
                </div>
              </div>
              
              {/* Item Details */}
              <div className="space-y-2">
                <h3 className="text-white font-medium text-sm line-clamp-2">
                  {item.name}
                </h3>
                <div className="flex items-center justify-between">
                  <Badge 
                    variant="secondary" 
                    className="bg-white/10 text-white/80 border-white/20 text-xs"
                  >
                    {item.category}
                  </Badge>
                  <span className="text-white/60 text-xs">{item.color}</span>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <div className="glass-panel glass-refraction p-8 rounded-3xl border-white/20 max-w-md mx-auto">
              <div className="text-white/50 mb-4">
                <Filter className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-white text-lg font-medium mb-2">No items found</h3>
              <p className="text-white/70 text-sm">
                Try adjusting your search or filter criteria
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Closet;