import { useState, useEffect } from "react";
import { Cloud, Sun, CloudRain, Wind, Plus } from "lucide-react";
import { Card } from "@/components/ui/card";
import useScrollRefraction from "@/hooks/useScrollRefraction";

const Home = () => {
  useScrollRefraction();
  const [weather, setWeather] = useState({
    location: "New York",
    temperature: 22,
    condition: "sunny",
    icon: Sun
  });

  const recommendations = [
    {
      id: 1,
      title: "Perfect for sunny weather",
      items: ["White linen shirt", "Beige chinos", "Brown leather loafers"],
      temperature: "22°C",
      style: "casual-chic"
    },
    {
      id: 2,
      title: "Evening elegance",
      items: ["Black blazer", "Dark jeans", "White sneakers"],
      temperature: "18°C",
      style: "smart-casual"
    },
    {
      id: 3,
      title: "Comfortable day out",
      items: ["Knit sweater", "Tailored trousers", "Canvas shoes"],
      temperature: "20°C",
      style: "relaxed"
    }
  ];

  const WeatherIcon = weather.icon;

  return (
    <div className="min-h-screen pb-24 pt-6">
      <div className="container mx-auto px-4 space-y-6">
        {/* Weather Card */}
        <Card className="glass-panel glass-refraction p-6 border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-light text-white mb-1">
                {weather.location}
              </h2>
              <p className="text-white/70 text-sm">Today</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-3xl font-light text-white">
                  {weather.temperature}°
                </div>
                <div className="text-white/70 text-sm capitalize">
                  {weather.condition}
                </div>
              </div>
              <WeatherIcon className="h-8 w-8 text-white/80" />
            </div>
          </div>
        </Card>

        {/* Stats Card */}
        <Card className="glass-panel glass-refraction p-6 border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-white mb-1">
                Your Wardrobe
              </h3>
              <p className="text-white/70">You have 47 items</p>
            </div>
            <div className="glass-panel p-3 rounded-full border-white/20">
              <Plus className="h-5 w-5 text-white" />
            </div>
          </div>
        </Card>

        {/* Recommendations */}
        <div>
          <h3 className="text-xl font-light text-white mb-4 px-2">
            Today's Recommendations
          </h3>
          
          <div className="space-y-4">
            {recommendations.map((rec, index) => (
              <Card 
                key={rec.id} 
                className="glass-panel glass-refraction p-6 border-white/20 transition-all duration-300 hover:transform hover:scale-[1.02]"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-medium text-white mb-1">
                      {rec.title}
                    </h4>
                    <p className="text-white/60 text-sm capitalize">
                      {rec.style} • {rec.temperature}
                    </p>
                  </div>
                  <div className="glass-panel px-3 py-1 rounded-full border-white/20">
                    <span className="text-white/80 text-xs">Suggested</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  {rec.items.map((item, itemIndex) => (
                    <div 
                      key={itemIndex}
                      className="flex items-center space-x-3 p-3 rounded-xl bg-white/5 border border-white/10"
                    >
                      <div className="w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center">
                        <span className="text-white/70 text-xs font-medium">
                          {itemIndex + 1}
                        </span>
                      </div>
                      <span className="text-white/90 text-sm">{item}</span>
                    </div>
                  ))}
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;