import { useState } from "react";
import { Calendar, ChevronLeft, ChevronRight, Plus, Sun, Cloud } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog";
import useScrollRefraction from "@/hooks/useScrollRefraction";

const Schedule = () => {
  useScrollRefraction();
  const [viewMode, setViewMode] = useState<"week" | "month">("week");
  const [selectedDate, setSelectedDate] = useState(new Date());

  const weekDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
  const currentWeek = Array.from({ length: 7 }, (_, i) => {
    const date = new Date();
    const day = date.getDay();
    const diff = date.getDate() - day + i + 1;
    return new Date(date.setDate(diff));
  });

  const outfitPlans = [
    {
      date: "2024-08-01",
      event: "Work Meeting",
      weather: { temp: 24, icon: Sun },
      outfit: ["Navy Blazer", "White Shirt", "Dark Jeans"],
      status: "planned"
    },
    {
      date: "2024-08-02",
      event: "Dinner Date",
      weather: { temp: 22, icon: Cloud },
      outfit: ["Black Dress", "Leather Jacket", "Heels"],
      status: "planned"
    },
    {
      date: "2024-08-03",
      event: "Weekend Casual",
      weather: { temp: 26, icon: Sun },
      outfit: ["Cotton T-Shirt", "Linen Shorts", "Sneakers"],
      status: "suggested"
    }
  ];

  return (
    <div className="min-h-screen pb-24 pt-6">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-light text-white">My Schedule</h1>
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === "week" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("week")}
              className={`${viewMode === "week" ? "bg-white/20 text-white" : "text-white/70 hover:bg-white/10"}`}
            >
              Week
            </Button>
            <Button
              variant={viewMode === "month" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("month")}
              className={`${viewMode === "month" ? "bg-white/20 text-white" : "text-white/70 hover:bg-white/10"}`}
            >
              Month
            </Button>
          </div>
        </div>

        {/* Calendar Navigation */}
        <Card className="glass-panel glass-refraction p-4 border-white/20 mb-6">
          <div className="flex items-center justify-between mb-4">
            <Button variant="ghost" size="icon" className="text-white hover:bg-white/10">
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h2 className="text-lg font-medium text-white">
              August 2024
            </h2>
            <Button variant="ghost" size="icon" className="text-white hover:bg-white/10">
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {viewMode === "week" && (
            <div className="grid grid-cols-7 gap-2">
              {weekDays.map((day, index) => (
                <div key={day} className="text-center">
                  <div className="text-white/70 text-xs mb-2">{day}</div>
                  <Button
                    variant="ghost"
                    className={`w-10 h-10 rounded-full text-white hover:bg-white/10 ${
                      currentWeek[index].getDate() === new Date().getDate()
                        ? "bg-white/20 border border-white/30"
                        : ""
                    }`}
                  >
                    {currentWeek[index].getDate()}
                  </Button>
                </div>
              ))}
            </div>
          )}
        </Card>

        {/* Outfit Plans */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-light text-white">Planned Outfits</h3>
            <Dialog>
              <DialogTrigger asChild>
                <Button className="glass-panel border-white/20 text-white hover:bg-white/10 rounded-full w-10 h-10 p-0">
                  <Plus className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="glass-panel border-white/20 bg-black/20 backdrop-blur-xl max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-white">Plan New Outfit</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 pt-4">
                  <div className="glass-panel p-3 rounded-xl border-white/20">
                    <input 
                      type="date" 
                      className="w-full bg-transparent text-white border-none outline-none"
                    />
                  </div>
                  <div className="glass-panel p-3 rounded-xl border-white/20">
                    <input 
                      type="text" 
                      placeholder="Event name"
                      className="w-full bg-transparent text-white border-none outline-none placeholder:text-white/50"
                    />
                  </div>
                  <Button className="w-full glass-panel border-white/20 text-white hover:bg-white/10">
                    Choose Outfit
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {outfitPlans.map((plan, index) => (
            <Card 
              key={plan.date}
              className="glass-panel glass-refraction p-6 border-white/20 transition-all duration-300 hover:transform hover:scale-[1.02] cursor-pointer"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="glass-panel p-2 rounded-full border-white/20">
                    <Calendar className="h-4 w-4 text-white/80" />
                  </div>
                  <div>
                    <h4 className="text-white font-medium">{plan.event}</h4>
                    <p className="text-white/60 text-sm">
                      {new Date(plan.date).toLocaleDateString('en-US', { 
                        weekday: 'long', 
                        month: 'short', 
                        day: 'numeric' 
                      })}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-1 text-white/70">
                    <plan.weather.icon className="h-4 w-4" />
                    <span className="text-sm">{plan.weather.temp}°</span>
                  </div>
                  <Badge 
                    variant={plan.status === "planned" ? "default" : "secondary"}
                    className={`${
                      plan.status === "planned" 
                        ? "bg-white/20 text-white border-white/30" 
                        : "bg-white/10 text-white/70 border-white/20"
                    }`}
                  >
                    {plan.status}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <h5 className="text-white/80 text-sm font-medium">Outfit:</h5>
                <div className="flex flex-wrap gap-2">
                  {plan.outfit.map((item, itemIndex) => (
                    <div 
                      key={itemIndex}
                      className="glass-panel px-3 py-1 rounded-full border-white/20 text-white/90 text-sm"
                    >
                      {item}
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {outfitPlans.length === 0 && (
          <div className="text-center py-12">
            <div className="glass-panel glass-refraction p-8 rounded-3xl border-white/20 max-w-md mx-auto">
              <div className="text-white/50 mb-4">
                <Calendar className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-white text-lg font-medium mb-2">No outfits planned</h3>
              <p className="text-white/70 text-sm mb-4">
                Start planning your outfits for the week ahead
              </p>
              <Button className="glass-panel border-white/20 text-white hover:bg-white/10">
                <Plus className="h-4 w-4 mr-2" />
                Plan First Outfit
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Schedule;