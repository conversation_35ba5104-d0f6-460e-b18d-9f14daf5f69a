import { useState } from "react";
import { Outlet, Link, useLocation } from "react-router-dom";
import { Home, Shirt, Calendar, User } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import heroBackground from "@/assets/hero-background.jpg";
import useScrollRefraction from "@/hooks/useScrollRefraction";

const AppLayout = () => {
  useScrollRefraction();
  const location = useLocation();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  // Track scroll for tab bar animation
  useState(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  });

  const navItems = [
    { icon: Home, label: "Home", path: "/app" },
    { icon: Shirt, label: "Closet", path: "/app/closet" },
    { icon: Calendar, label: "Schedule", path: "/app/schedule" },
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <div className="min-h-screen overflow-x-hidden">
      {/* Global Background */}
      <div 
        className="fixed inset-0 z-0"
        style={{
          backgroundImage: `url(${heroBackground})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed'
        }}
      />
      <div className="fixed inset-0 z-0 bg-black/40" />
      
      {/* Top Bar with Profile */}
      <div className="fixed top-0 left-0 right-0 z-40 p-4">
        <div className="flex justify-end">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsProfileOpen(true)}
            className="glass-panel border-white/20 text-white hover:bg-white/10 rounded-full w-12 h-12"
          >
            <User className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 pt-16">
        <Outlet />
      </div>

      {/* Bottom Navigation */}
      <div 
        className={`fixed bottom-0 left-0 right-0 z-50 transition-all duration-300 ${
          scrollY > 100 ? 'transform translate-y-full opacity-0' : 'transform translate-y-0 opacity-100'
        }`}
      >
        <div className="glass-panel glass-refraction mx-4 mb-4 rounded-3xl border-white/20 p-4">
          <div className="flex items-center justify-around">
            {navItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.path);
              
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`flex flex-col items-center space-y-1 px-4 py-2 rounded-2xl transition-all duration-200 ${
                    active 
                      ? 'bg-white/20 text-white transform scale-105' 
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <Icon className="h-5 w-5" />
                  <span className="text-xs font-medium">{item.label}</span>
                </Link>
              );
            })}
          </div>
        </div>
      </div>

      {/* Profile Modal */}
      <Dialog open={isProfileOpen} onOpenChange={setIsProfileOpen}>
        <DialogContent className="glass-panel border-white/20 bg-black/20 backdrop-blur-xl max-w-md">
          <DialogHeader>
            <DialogTitle className="text-white text-center">Profile</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6 pt-4">
            {/* Profile Photo */}
            <div className="flex justify-center">
              <div className="relative">
                <Avatar className="w-24 h-24 glass-panel border-2 border-white/30">
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-white/10 text-white text-xl">
                    JD
                  </AvatarFallback>
                </Avatar>
                <Button
                  size="icon"
                  className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full glass-panel border-white/20 text-white hover:bg-white/10"
                >
                  <User className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Profile Fields */}
            <div className="space-y-4">
              <div>
                <label className="text-white/80 text-sm mb-2 block">Name</label>
                <Input
                  placeholder="Enter your name"
                  defaultValue="Jane Doe"
                  className="glass-panel border-white/20 bg-white/5 text-white placeholder:text-white/50"
                />
              </div>
              
              <div>
                <label className="text-white/80 text-sm mb-2 block">Location</label>
                <Input
                  placeholder="Enter your location"
                  defaultValue="New York, NY"
                  className="glass-panel border-white/20 bg-white/5 text-white placeholder:text-white/50"
                />
              </div>
              
              <div>
                <label className="text-white/80 text-sm mb-2 block">Date of Birth</label>
                <Input
                  type="date"
                  className="glass-panel border-white/20 bg-white/5 text-white"
                />
              </div>
            </div>

            {/* Actions */}
            <div className="flex space-x-3 pt-4">
              <Button 
                variant="ghost" 
                onClick={() => setIsProfileOpen(false)}
                className="flex-1 glass-panel border-white/20 text-white hover:bg-white/10"
              >
                Cancel
              </Button>
              <Button 
                onClick={() => setIsProfileOpen(false)}
                className="flex-1 bg-white/20 text-white hover:bg-white/30 border-white/30"
              >
                Save
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AppLayout;