import { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import mockupHome from "@/assets/mockup-home.jpg";
import mockupCloset from "@/assets/mockup-closet.jpg";
import mockupSchedule from "@/assets/mockup-schedule.jpg";

const MockupShowcase = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const mockups = [
    {
      title: "Home Dashboard",
      description: "Weather-aware outfit recommendations at a glance",
      image: mockupHome
    },
    {
      title: "Smart Closet",
      description: "Organize and browse your wardrobe effortlessly",
      image: mockupCloset
    },
    {
      title: "Schedule Planner",
      description: "Plan your outfits for upcoming events and occasions",
      image: mockupSchedule
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % mockups.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + mockups.length) % mockups.length);
  };

  return (
    <section className="py-24 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light text-white mb-6 text-shadow">
            Experience ClosetMate
          </h2>
          <p className="text-xl text-white/80 max-w-2xl mx-auto font-light">
            Discover how our intuitive interface transforms your daily styling routine
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative max-w-4xl mx-auto">
          {/* Mockup Display */}
          <div className="glass-panel glass-refraction p-6 rounded-3xl overflow-hidden">
            <div className="relative h-96 md:h-[500px] rounded-2xl overflow-hidden bg-white/5">
              {mockups.map((mockup, index) => (
                <div
                  key={index}
                  className={`absolute inset-0 transition-all duration-700 ${
                    index === currentSlide 
                      ? 'opacity-100 transform translate-x-0' 
                      : index < currentSlide 
                        ? 'opacity-0 transform -translate-x-full' 
                        : 'opacity-0 transform translate-x-full'
                  }`}
                >
                  {/* Device Frame */}
                  <div className="h-full p-8 flex items-center justify-center">
                    <div className="relative w-full max-w-md mx-auto">
                      {/* Phone/Device Frame */}
                      <div className="relative bg-white/10 backdrop-blur-sm rounded-[2.5rem] p-6 border border-white/20 shadow-2xl">
                        <div className="relative aspect-[9/16] rounded-[1.5rem] overflow-hidden bg-white shadow-inner">
                          <img 
                            src={mockup.image} 
                            alt={mockup.title}
                            className="w-full h-full object-cover object-top"
                          />
                          {/* Screen Overlay for Glass Effect */}
                          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/5"></div>
                        </div>
                        {/* Device Details */}
                        <div className="absolute top-3 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-white/30 rounded-full"></div>
                      </div>
                      
                      {/* Info Panel */}
                      <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 text-center">
                        <div className="glass-panel px-6 py-3 rounded-full border border-white/20">
                          <h3 className="text-lg font-medium text-white mb-1">
                            {mockup.title}
                          </h3>
                          <p className="text-sm text-white/70">
                            {mockup.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-center items-center mt-8 space-x-4">
            <Button
              variant="outline"
              size="icon"
              className="glass-panel border-white/20 text-white hover:bg-white/10"
              onClick={prevSlide}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {/* Dots Indicator */}
            <div className="flex space-x-2">
              {mockups.map((_, index) => (
                <button
                  key={index}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentSlide 
                      ? 'bg-white' 
                      : 'bg-white/30 hover:bg-white/50'
                  }`}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </div>

            <Button
              variant="outline"
              size="icon"
              className="glass-panel border-white/20 text-white hover:bg-white/10"
              onClick={nextSlide}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MockupShowcase;