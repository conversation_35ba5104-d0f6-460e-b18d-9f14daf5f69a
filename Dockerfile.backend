# Backend Dockerfile for Closet Glass Chic
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    curl \
    dumb-init

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# Development stage
FROM base AS development

# Copy package files
COPY backend/package*.json ./
RUN npm ci

# Copy source code
COPY backend/ .

# Expose port
EXPOSE 3001

# Start development server
CMD ["dumb-init", "npm", "run", "dev"]

# Production stage
FROM node:18-alpine AS production

# Install system dependencies
RUN apk add --no-cache dumb-init

# Create app directory
WORKDIR /app

# Create non-root user and group
RUN addgroup -g 1001 -S nodejs && \
    adduser -S backend -u 1001

# Copy built files from builder
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/src ./src
COPY --from=builder /app/uploads ./uploads

# Set permissions
RUN mkdir -p uploads && \
    chown -R appuser:appgroup /app && \
    chown -R appuser:appgroup uploads

USER appuser

EXPOSE 3001

CMD ["dumb-init", "npm", "run", "start"]

# Build stage
FROM base AS builder

# Copy package files and install all dependencies
COPY backend/package*.json ./
RUN npm ci

# Copy source code
COPY backend/ .

# Build the application
RUN npm run build

# Production stage
FROM base AS production

# Copy package files and install only production dependencies
COPY backend/package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package.json ./

# Create uploads directory
RUN mkdir -p uploads && \
    chown -R backend:nodejs /app && \
    chown -R backend:nodejs uploads

# Switch to non-root user
USER backend

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Expose port
EXPOSE 3001

# Start the application
CMD ["dumb-init", "node", "dist/index.js"]
